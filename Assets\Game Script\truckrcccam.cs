using UnityEngine;
using System.Collections;

/// <summary>
/// Truck RCC Camera Controller - Develop by <PERSON>
/// Manages camera settings specifically for truck vehicles using RCC system
/// Provides automatic camera configuration and manual override capabilities
/// </summary>
public class TruckRCCCam : MonoBehaviour
{
    [Header("Camera Configuration")]
    [SerializeField] private RCC_Camera rccCamera;

    [Header("Truck Camera Settings")]
    [SerializeField] [Range(1f, 15f)] private float truckCameraHeight = 4f;
    [SerializeField] [Range(3f, 25f)] private float truckCameraDistance = 12f;
    [SerializeField] [Range(10f, 90f)] private float truckCameraFOV = 65f;

    [Header("Vehicle Reference")]
    [SerializeField] private GameObject targetTruck;

    [Header("Camera Behavior")]
    [SerializeField] private bool autoConfigureOnStart = true;
    [SerializeField] private bool useDefaultValuesAsFallback = true;
    [SerializeField] private float configurationDelay = 0.1f;

    [Header("Default Fallback Values")]
    [SerializeField] private float defaultHeight = 2f;
    [SerializeField] private float defaultDistance = 6f;
    [SerializeField] private float defaultFOV = 60f;

    // Private variables
    private RCC_CarControllerV4 truckController;
    private bool isConfigured = false;

    #region Unity Lifecycle

    private void Awake()
    {
        ValidateComponents();
    }

    private void Start()
    {
        if (autoConfigureOnStart)
        {
            StartCoroutine(ConfigureCameraWithDelay());
        }
    }

    #endregion

    #region Camera Configuration

    /// <summary>
    /// Configures camera settings with a small delay to ensure all components are initialized
    /// </summary>
    private IEnumerator ConfigureCameraWithDelay()
    {
        yield return new WaitForSeconds(configurationDelay);
        ConfigureTruckCamera();
    }

    /// <summary>
    /// Main method to configure camera settings for truck
    /// </summary>
    public void ConfigureTruckCamera()
    {
        if (!ValidateComponents())
        {
            Debug.LogWarning($"[TruckRCCCam] Cannot configure camera - missing components on {gameObject.name}");
            return;
        }

        // Check if truck controller is enabled and valid
        if (truckController != null && truckController.enabled)
        {
            ApplyTruckCameraSettings();
            Debug.Log($"[TruckRCCCam] Camera configured for truck: Height={truckCameraHeight}, Distance={truckCameraDistance}, FOV={truckCameraFOV}");
        }
        else if (useDefaultValuesAsFallback)
        {
            ApplyDefaultCameraSettings();
            Debug.Log($"[TruckRCCCam] Applied default camera settings as fallback");
        }
        else
        {
            Debug.LogWarning($"[TruckRCCCam] Truck controller not available and fallback disabled");
        }

        isConfigured = true;
    }

    /// <summary>
    /// Applies truck-specific camera settings
    /// </summary>
    private void ApplyTruckCameraSettings()
    {
        if (rccCamera == null) return;

        // Set camera to TPS mode
        rccCamera.ChangeCamera(RCC_Camera.CameraMode.TPS);

        // Apply truck-specific settings
        rccCamera.TPSHeight = truckCameraHeight;
        rccCamera.TPSDistance = truckCameraDistance;
        rccCamera.TPSMaximumFOV = truckCameraFOV;
        rccCamera.TPSMinimumFOV = truckCameraFOV - 10f; // Slightly lower minimum FOV

        // Optional: Set truck as camera target if not already set
        if (rccCamera.cameraTarget.playerVehicle != truckController)
        {
            rccCamera.SetTarget(truckController);
        }
    }

    /// <summary>
    /// Applies default camera settings as fallback
    /// </summary>
    private void ApplyDefaultCameraSettings()
    {
        if (rccCamera == null) return;

        rccCamera.ChangeCamera(RCC_Camera.CameraMode.TPS);
        rccCamera.TPSHeight = defaultHeight;
        rccCamera.TPSDistance = defaultDistance;
        rccCamera.TPSMaximumFOV = defaultFOV;
        rccCamera.TPSMinimumFOV = defaultFOV - 10f;
    }

    #endregion

    #region Public Methods

    /// <summary>
    /// Manually set truck camera height
    /// </summary>
    public void SetCameraHeight(float height)
    {
        truckCameraHeight = Mathf.Clamp(height, 1f, 15f);
        if (isConfigured && rccCamera != null)
        {
            rccCamera.TPSHeight = truckCameraHeight;
        }
    }

    /// <summary>
    /// Manually set truck camera distance
    /// </summary>
    public void SetCameraDistance(float distance)
    {
        truckCameraDistance = Mathf.Clamp(distance, 3f, 25f);
        if (isConfigured && rccCamera != null)
        {
            rccCamera.TPSDistance = truckCameraDistance;
        }
    }

    /// <summary>
    /// Manually set truck camera FOV
    /// </summary>
    public void SetCameraFOV(float fov)
    {
        truckCameraFOV = Mathf.Clamp(fov, 10f, 90f);
        if (isConfigured && rccCamera != null)
        {
            rccCamera.TPSMaximumFOV = truckCameraFOV;
            rccCamera.TPSMinimumFOV = truckCameraFOV - 10f;
        }
    }

    /// <summary>
    /// Reset camera to default truck settings
    /// </summary>
    public void ResetToTruckDefaults()
    {
        truckCameraHeight = 4f;
        truckCameraDistance = 12f;
        truckCameraFOV = 65f;

        if (isConfigured)
        {
            ApplyTruckCameraSettings();
        }
    }

    /// <summary>
    /// Force reconfigure camera (useful when truck changes)
    /// </summary>
    public void ReconfigureCamera()
    {
        isConfigured = false;
        ConfigureTruckCamera();
    }

    #endregion

    #region Validation and Utilities

    /// <summary>
    /// Validates all required components
    /// </summary>
    private bool ValidateComponents()
    {
        // Auto-find RCC Camera if not assigned
        if (rccCamera == null)
        {
            rccCamera = FindObjectOfType<RCC_Camera>();
            if (rccCamera == null)
            {
                Debug.LogError($"[TruckRCCCam] No RCC_Camera found in scene! Please assign one.");
                return false;
            }
        }

        // Auto-find truck if not assigned
        if (targetTruck == null)
        {
            // Try to find truck by tag first
            GameObject foundTruck = GameObject.FindGameObjectWithTag("Vehicle");
            if (foundTruck != null && foundTruck.GetComponent<RCC_CarControllerV4>() != null)
            {
                targetTruck = foundTruck;
            }
            else
            {
                Debug.LogWarning($"[TruckRCCCam] No truck assigned and none found automatically");
                return false;
            }
        }

        // Get truck controller component
        if (targetTruck != null)
        {
            truckController = targetTruck.GetComponent<RCC_CarControllerV4>();
            if (truckController == null)
            {
                Debug.LogError($"[TruckRCCCam] Target truck {targetTruck.name} does not have RCC_CarControllerV4 component!");
                return false;
            }
        }

        return true;
    }

    #endregion

    #region Editor Helpers

    #if UNITY_EDITOR

    /// <summary>
    /// Validate components in editor
    /// </summary>
    private void OnValidate()
    {
        // Clamp values to valid ranges
        truckCameraHeight = Mathf.Clamp(truckCameraHeight, 1f, 15f);
        truckCameraDistance = Mathf.Clamp(truckCameraDistance, 3f, 25f);
        truckCameraFOV = Mathf.Clamp(truckCameraFOV, 10f, 90f);

        defaultHeight = Mathf.Clamp(defaultHeight, 1f, 15f);
        defaultDistance = Mathf.Clamp(defaultDistance, 3f, 25f);
        defaultFOV = Mathf.Clamp(defaultFOV, 10f, 90f);

        configurationDelay = Mathf.Max(0f, configurationDelay);
    }

    #endif

    #endregion
}
